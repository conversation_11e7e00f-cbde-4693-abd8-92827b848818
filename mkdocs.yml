site_name: Kubernetes Gateway API Inference Extension
repo_url: https://github.com/kubernetes-sigs/gateway-api-inference-extension
repo_name: kubernetes-sigs/gateway-api-inference-extension
site_dir: site
docs_dir: site-src
extra_css:
  - stylesheets/extra.css
theme:
  name: material
  icon:
    repo: fontawesome/brands/git-alt
  logo: images/logo/logo-text-large-horizontal-white.png
  favicon: images/favicon-64.png
  features:
    - search.highlight
    - navigation.tabs
    - navigation.top
    - navigation.expand
  palette:
    primary: custom
  custom_dir: site-src/overrides
edit_uri: edit/main/site-src/
plugins:
  - search
  - awesome-pages
  - macros:
      j2_line_comment_prefix: "#$"
  - mermaid2
markdown_extensions:
  - admonition
  - meta
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.details
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.snippets
  - toc:
      permalink: true
  - tables
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
nav:
  - Overview:
    - Introduction: index.md
    - Concepts:
        API Overview: concepts/api-overview.md
        Design Principles: concepts/design-principles.md
        Conformance: concepts/conformance.md
        Roles and Personas: concepts/roles-and-personas.md
    - Implementations: 
      - Gateways: implementations/gateways.md
      - Model Servers: implementations/model-servers.md
    - FAQ: faq.md
  - Guides:
    - User Guides:
      - Getting started: guides/index.md
      - Use Cases:
        - Serve Multiple GenAI models: guides/serve-multiple-genai-models.md
        - Serve Multiple LoRA adapters: guides/serve-multiple-lora-adapters.md
      - Rollout:
        - Adapter Rollout: guides/adapter-rollout.md
        - InferencePool Rollout: guides/inferencepool-rollout.md
      - Metrics and Observability: guides/metrics-and-observability.md
      - Configuration Guide:
          - Configuring the plugins via configuration files or text: guides/epp-configuration/config-text.md    
          - Prefix Cache Aware Plugin: guides/epp-configuration/prefix-aware.md
    - Implementer Guides:
      - Getting started: guides/implementers.md
      - Conformance Tests: guides/conformance-tests.md
  - Performance:
    - Benchmark: performance/benchmark/index.md
    - Regression Testing: performance/regression-testing/index.md
  - Reference:
    - API Reference: reference/spec.md
    - API Types:
      - InferencePool: api-types/inferencepool.md
      - InferenceModel: api-types/inferencemodel.md
  - Enhancements:
    - Overview: gieps/overview.md
  - Contributing:
    - How to Get Involved: contributing/index.md
    - Developer Guide: contributing/devguide.md
