apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "gateway-api-inference-extension.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway-api-inference-extension.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.inferenceExtension.replicas | default 1 }}
  selector:
    matchLabels:
      {{- include "gateway-api-inference-extension.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "gateway-api-inference-extension.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "gateway-api-inference-extension.name" . }}
      # Conservatively, this timeout should mirror the longest grace period of the pods within the pool
      terminationGracePeriodSeconds: 130
      containers:
      - name: epp
        image: {{ .Values.inferenceExtension.image.hub }}/{{ .Values.inferenceExtension.image.name }}:{{ .Values.inferenceExtension.image.tag }}
        imagePullPolicy: {{ .Values.inferenceExtension.image.pullPolicy | default "Always" }}
        args:
        - --pool-name
        - {{ .Release.Name }}
        - --pool-namespace
        - {{ .Release.Namespace }}
        - --v
        - "3"
        - --grpc-port
        - "9002"
        - --grpc-health-port
        - "9003"
        - --metrics-port
        - "9090"
        - --config-file
        - "config/{{ .Values.inferenceExtension.pluginsConfigFile }}"
        # https://pkg.go.dev/flag#hdr-Command_line_flag_syntax; space is only for non-bool flags
        - "--enable-pprof={{ .Values.inferenceExtension.enablePprof }}"
        {{- if eq (.Values.inferencePool.modelServerType | default "vllm") "triton-tensorrt-llm" }}
        - --total-queued-requests-metric
        - "nv_trt_llm_request_metrics{request_type=waiting}"
        - --kv-cache-usage-percentage-metric
        - "nv_trt_llm_kv_cache_block_metrics{kv_cache_block_type=fraction}"
        - --lora-info-metric
        - "" # Set an empty metric to disable LoRA metric scraping as they are not supported by Triton yet.
        {{- end }}
        ports:
        - name: grpc
          containerPort: 9002
        - name: grpc-health
          containerPort: 9003
        - name: metrics
          containerPort: 9090
        livenessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        env:
        {{- range $key, $value := .Values.inferenceExtension.env }}
        - name: {{ $key }}
          value: {{ $value | quote }}
        {{- end }}
        volumeMounts:
        - name: plugins-config-volume
          mountPath: "/config"
      volumes:
      - name: plugins-config-volume
        configMap:
          name: {{ include "gateway-api-inference-extension.name" . }}
